import {
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantStartGameTokenData,
    PaymentRequest
} from "@skywind-group/sw-wallet-adapter-core";
import {
    BrokenGameRequest,
    CommitBonusPaymentRequest,
    GetFreeBetInfoRequest,
    KeepAliveRequest,
    RegulatoryActionRequest,
    TransferRequest
} from "@skywind-group/sw-integration-core";
import { IsDefined, IsIn, IsNotEmpty, IsOptional } from "class-validator";

// Base operator request/response interfaces
export interface OperatorBaseRequest {
    customer: string;
    token: string;
    hash?: string;
}

export interface OperatorBaseResponse {
    code: number;
    status: string;
    currency?: string;
    balance?: number;
    bonusBalance?: number;
    trxId?: number;
    creditTrxId?: number;
    traderId?: number;
}

export interface OperatorError {
    code: number;
    status: string;
}

// Authentication
export interface OperatorAuthRequest extends OperatorBaseRequest {}

export interface OperatorAuthResponse extends OperatorBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    traderId: number;
}

// Transaction operations
export interface OperatorDebitRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    tip?: boolean;
}

export interface OperatorDebitResponse extends OperatorBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    trxId: number;
}

export interface OperatorCreditRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    freespin?: OperatorFreespinInfo;
}

export interface OperatorCreditResponse extends OperatorBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    trxId?: number;
}

export interface OperatorDebitCreditRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    creditAmount: number;
    currency: string;
    betId: string;
    trxId: string;
    creditTrxId: string;
    tip?: boolean;
}

export interface OperatorDebitCreditResponse extends OperatorBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    trxId: number;
    creditTrxId: number;
}

export interface OperatorRollbackRequest extends OperatorBaseRequest {
    gameId: string;
    trxId: string;
}

export interface OperatorRollbackResponse extends OperatorBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    trxId: number;
}

// Promo operations
export interface OperatorPromoRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    promo: {
        promoType: string;
        promoRef: string;
        freeSpinData?: {
            remainingRounds?: number;
            totalWinnings?: number;
            requested?: boolean;
        };
    };
}

export interface OperatorPromoResponse extends OperatorBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    trxId: number;
}

// Freespin info
export interface OperatorFreespinInfo {
    freespinRef: string;
    requested?: boolean;
    remainingRounds?: number;
    totalWinnings?: number;
}

// operator-specific enums and interfaces
export enum OperatorPlatformType {
    desktop = "d",
    mobile = "m"
}

export enum OperatorPromoType {
    FSW = "FSW", // freespin
    JPW = "JPW", // jackpot
    CB = "CB",   // cashBack
    TW = "TW",   // tournament win
    RW = "RW",   // reward
    REW = "REW", // red envelope win
    CDW = "CDW", // cash drop win
    RB = "RB"    // rakeBack
}

// Integration-specific entities
export class IntegrationGameLaunchRequest {
    @IsDefined()
    @IsNotEmpty()
    gameId: string;

    @IsDefined()
    @IsNotEmpty()
    lang: string;

    @IsDefined()
    @IsNotEmpty()
    trader: string;

    @IsDefined()
    @IsNotEmpty()
    platform: string;

    @IsOptional()
    @IsNotEmpty()
    currency?: string;

    @IsOptional()
    @IsNotEmpty()
    customer?: string;

    @IsOptional()
    @IsNotEmpty()
    token?: string;

    @IsOptional()
    @IsNotEmpty()
    tableId?: string;

    @IsOptional()
    @IsNotEmpty()
    lobby?: string;

    @IsOptional()
    @IsNotEmpty()
    country?: string;

    @IsOptional()
    demo?: boolean;
}

export interface IntegrationStartGameTokenData extends MerchantStartGameTokenData {
    relaunchUrl?: string;
    loginFailed?: boolean;
}

export interface IntegrationGameTokenData extends MerchantGameTokenData {}

export interface IntegrationInitRequest extends MerchantGameInitRequest {
    previousStartTokenData?: IntegrationStartGameTokenData;
    channelId: number;
    history_url?: string;
    platform?: string;
}

export interface IntegrationPaymentRequest extends CommitBonusPaymentRequest<IntegrationGameTokenData> {
    request: PaymentRequest;
    operatorRoundId?: string;
}

export interface IntegrationTransferRequest extends TransferRequest<IntegrationGameTokenData> {}

export interface IntegrationKeepAliveRequest extends KeepAliveRequest<IntegrationGameTokenData> {}

export interface IntegrationBrokenGameRequest extends BrokenGameRequest<IntegrationGameTokenData> {}

export interface IntegrationGetFreeBetInfoRequest extends GetFreeBetInfoRequest<IntegrationGameTokenData> {}

export interface IntegrationRegulatoryActionRequest extends RegulatoryActionRequest<IntegrationGameTokenData> {}

// operator-specific interfaces
export interface OperatorFreeSpinData {
    freespinRef: string;
    requested?: boolean;
    remainingRounds?: number;
    totalWinnings?: number;
}

export interface OperatorFreespinRequest {
    customers: string[];
    games: string[];
    numberOfFreespins: number;
    freespinRef: string;
    wagerRequirement: number;
    validFrom: Date;
    validUntil: Date;
    maxWin?: number;
    costPerBet?: number;
    coinValueLevel?: number;
    lines?: number;
    coins?: number;
    denomination?: number;
    betPerLine?: number;
}

export interface OperatorRoundDetailsRequest {
    customer: string;
    roundId: string;
    gameId: string;
    lang: string;
    trader?: string;
}

export interface OperatorRoundDetailsResponse {
    code: number;
    status: string;
    roundId?: string;
    gameId?: string;
    customer?: string;
    currency?: string;
    totalBet?: number;
    totalWin?: number;
    startTime?: Date;
    endTime?: Date;
    transactions?: OperatorTransactionDetails[];
    json?: any;
    image?: {
        url: string;
        height: number;
        width: number;
    };
}

export interface OperatorTransactionDetails {
    trxId: string;
    type: string;
    amount: number;
    timestamp: Date;
    status: string;
}

// History
export class OperatorHistoryRequest {
    @IsDefined()
    @IsNotEmpty()
    customer: string;

    @IsDefined()
    @IsNotEmpty()
    roundId: string;

    @IsDefined()
    @IsNotEmpty()
    gameId: string;

    @IsDefined()
    @IsNotEmpty()
    lang: string;

    @IsOptional()
    @IsNotEmpty()
    trader?: string;
}

// Error codes specific to operator
export const operatorErrorCodes = {
    SUCCESS: 0,
    UNKNOWN_ERROR: -1,
    UNAUTHORIZED_REQUEST: -2,
    NOT_INTEGRATED: -3,
    TOKEN_CUSTOMER_MISMATCH: -4,
    UNSUPPORTED_API_VERSION: -5,
    INTERNAL_CACHE_ERROR: -6,
    PROMOTION_TYPE_NOT_SUPPORTED: -7,
    BET_RECORD_NOT_FOUND: -20120,
    BET_ALREADY_WON: -20112,
    AUTHENTICATION_FAILED: -20101,
    GAME_NOT_FOUND: -20130,
    BET_LIMIT_REACHED: -20201,
    LOSS_LIMIT_REACHED: -20202,
    SESSION_LIMIT_REACHED: -20203,
    PROFIT_LIMIT_REACHED: -20204,
    INVALID_CASINO_VENDOR: -20301,
    ALL_BET_ARE_OFF: -20302,
    INVALID_GAME: -20303,
    CUSTOMER_NOT_FOUND: -20304,
    INVALID_CURRENCY: -20305,
    INSUFFICIENT_FUNDS: -20306,
    PLAYER_SUSPENDED: -20307,
    REQUIRED_FIELD_MISSING: -20308,
    DATA_OUT_OF_RANGE: -20309,
    BET_ALREADY_SETTLED: -20310,
    TOKEN_NOT_FOUND: -20316,
    TOKEN_TIMEOUT: -20311,
    TOKEN_INVALID: -20312,
    TRANSACTION_NOT_FOUND: -20313,
    NEGATIVE_DEPOSIT: -20314,
    NEGATIVE_WITHDRAWAL: -20315
};

// Status messages
export const operatorStatusMessages = {
    [operatorErrorCodes.SUCCESS]: "SUCCESS",
    [operatorErrorCodes.UNKNOWN_ERROR]: "UNKNOWN_ERROR",
    [operatorErrorCodes.UNAUTHORIZED_REQUEST]: "UNAUTHORIZED_REQUEST",
    [operatorErrorCodes.NOT_INTEGRATED]: "NOT_INTEGRATED",
    [operatorErrorCodes.TOKEN_CUSTOMER_MISMATCH]: "TOKEN_CUSTOMER_MISMATCH",
    [operatorErrorCodes.UNSUPPORTED_API_VERSION]: "UNSUPPORTED_API_VERSION",
    [operatorErrorCodes.INTERNAL_CACHE_ERROR]: "INTERNAL_CACHE_ERROR",
    [operatorErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED]: "PROMOTION_TYPE_NOT_SUPPORTED",
    [operatorErrorCodes.BET_RECORD_NOT_FOUND]: "BET_RECORD_NOT_FOUND",
    [operatorErrorCodes.BET_ALREADY_WON]: "BET_ALREADY_WON",
    [operatorErrorCodes.AUTHENTICATION_FAILED]: "AUTHENTICATION_FAILED",
    [operatorErrorCodes.GAME_NOT_FOUND]: "GAME_NOT_FOUND",
    [operatorErrorCodes.BET_LIMIT_REACHED]: "BET_LIMIT_REACHED",
    [operatorErrorCodes.LOSS_LIMIT_REACHED]: "LOSS_LIMIT_REACHED",
    [operatorErrorCodes.SESSION_LIMIT_REACHED]: "SESSION_LIMIT_REACHED",
    [operatorErrorCodes.PROFIT_LIMIT_REACHED]: "PROFIT_LIMIT_REACHED",
    [operatorErrorCodes.INVALID_CASINO_VENDOR]: "INVALID_CASINO_VENDOR",
    [operatorErrorCodes.ALL_BET_ARE_OFF]: "ALL_BET_ARE_OFF",
    [operatorErrorCodes.INVALID_GAME]: "INVALID_GAME",
    [operatorErrorCodes.CUSTOMER_NOT_FOUND]: "CUSTOMER_NOT_FOUND",
    [operatorErrorCodes.INVALID_CURRENCY]: "INVALID_CURRENCY",
    [operatorErrorCodes.INSUFFICIENT_FUNDS]: "INSUFFICIENT_FUNDS",
    [operatorErrorCodes.PLAYER_SUSPENDED]: "PLAYER_SUSPENDED",
    [operatorErrorCodes.REQUIRED_FIELD_MISSING]: "REQUIRED_FIELD_MISSING",
    [operatorErrorCodes.DATA_OUT_OF_RANGE]: "DATA_OUT_OF_RANGE",
    [operatorErrorCodes.BET_ALREADY_SETTLED]: "BET_ALREADY_SETTLED",
    [operatorErrorCodes.TOKEN_NOT_FOUND]: "TOKEN_NOT_FOUND",
    [operatorErrorCodes.TOKEN_TIMEOUT]: "TOKEN_TIMEOUT",
    [operatorErrorCodes.TOKEN_INVALID]: "TOKEN_INVALID",
    [operatorErrorCodes.TRANSACTION_NOT_FOUND]: "TRANSACTION_NOT_FOUND",
    [operatorErrorCodes.NEGATIVE_DEPOSIT]: "NEGATIVE_DEPOSIT",
    [operatorErrorCodes.NEGATIVE_WITHDRAWAL]: "NEGATIVE_WITHDRAWAL"
};

// Backward compatibility exports (deprecated - use operator* instead)
export const pronetErrorCodes = operatorErrorCodes;
export const pronetStatusMessages = operatorStatusMessages;
export type PronetError = OperatorError;
export type PronetBaseRequest = OperatorBaseRequest;
export type PronetBaseResponse = OperatorBaseResponse;
export type PronetAuthRequest = OperatorAuthRequest;
export type PronetAuthResponse = OperatorAuthResponse;
export type PronetDebitRequest = OperatorDebitRequest;
export type PronetDebitResponse = OperatorDebitResponse;
export type PronetCreditRequest = OperatorCreditRequest;
export type PronetCreditResponse = OperatorCreditResponse;
export type PronetDebitCreditRequest = OperatorDebitCreditRequest;
export type PronetDebitCreditResponse = OperatorDebitCreditResponse;
export type PronetRollbackRequest = OperatorRollbackRequest;
export type PronetRollbackResponse = OperatorRollbackResponse;
export type PronetPromoRequest = OperatorPromoRequest;
export type PronetPromoResponse = OperatorPromoResponse;
export type PronetFreespinInfo = OperatorFreespinInfo;
export const PronetHistoryRequest = OperatorHistoryRequest;

// PCES backward compatibility exports
export const pcesErrorCodes = operatorErrorCodes;
export const pcesStatusMessages = operatorStatusMessages;
export type PCESError = OperatorError;
export type PCESBaseRequest = OperatorBaseRequest;
export type PCESBaseResponse = OperatorBaseResponse;
export type PCESAuthRequest = OperatorAuthRequest;
export type PCESAuthResponse = OperatorAuthResponse;
export type PCESDebitRequest = OperatorDebitRequest;
export type PCESDebitResponse = OperatorDebitResponse;
export type PCESCreditRequest = OperatorCreditRequest;
export type PCESCreditResponse = OperatorCreditResponse;
export type PCESDebitCreditRequest = OperatorDebitCreditRequest;
export type PCESDebitCreditResponse = OperatorDebitCreditResponse;
export type PCESRollbackRequest = OperatorRollbackRequest;
export type PCESRollbackResponse = OperatorRollbackResponse;
export type PCESPromoRequest = OperatorPromoRequest;
export type PCESPromoResponse = OperatorPromoResponse;
export type PCESFreespinInfo = OperatorFreespinInfo;
export const PCESHistoryRequest = OperatorHistoryRequest;
