import { HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";
import config from "@config";
import { OperatorBaseRequest } from "@entities/operator.entities";
import { mapOperatorToSWError } from "@errors/operator.errors";
import { calculateOperatorHash, buildOperatorApiUrl, buildOperatorHeaders, isOperatorSuccess } from "@utils/operator.utils";

export interface HttpHandlerRequest {
    endpoint: string;
    method: "get" | "post" | "put" | "delete";
    payload?: any;
    merchantInfo: MerchantInfo;
    retryAvailable?: boolean;
}

export abstract class BaseHttpHandler {
    protected buildBaseRequest(customer: string, token: string): OperatorBaseRequest {
        return {
            customer,
            token
        };
    }

    protected buildHttpRequest(request: HttpHandlerRequest): HTTPOperatorRequest {
        const url = buildOperatorApiUrl(request.endpoint);

        // Calculate hash for the request payload
        const hash = request.payload ? calculateOperatorHash(request.payload) : "";

        // Add hash to payload if it exists
        if (request.payload) {
            request.payload.hash = hash;
        }

        return {
            url,
            method: request.method,
            headers: buildOperatorHeaders(hash),
            payload: request.payload,
            timeout: config.http.defaultOptions.timeout,
            proxy: config.http.defaultOptions.proxy,
            ssl: config.http.ssl,
            retryAvailable: request.retryAvailable !== false
        };
    }

    protected parseHttpResponse<T>(response: superagent.Response): T {
        const responseBody = response.body;

        // Check if operator response indicates success
        if (!isOperatorSuccess(responseBody)) {
            throw mapOperatorToSWError({
                code: responseBody.code || -1,
                status: responseBody.status || "Unknown error"
            });
        }

        return responseBody as T;
    }

    protected getGameTokenData(req: any): any {
        return req.gameTokenData || req.request?.gameTokenData;
    }

    protected buildOperatorRequest(
        customer: string,
        token: string,
        additionalFields: any = {}
    ): any {
        return {
            ...this.buildBaseRequest(customer, token),
            ...additionalFields
        };
    }

    protected validateRequiredFields(obj: any, fields: string[]): void {
        const missingFields = fields.filter(field => !obj[field]);
        if (missingFields.length > 0) {
            throw new Error(`Missing required fields: ${missingFields.join(", ")}`);
        }
    }

    protected sanitizeAmount(amount: number): number {
        // pronet expects amounts in the currency's minor units (e.g., cents for USD)
        return Math.round(amount * 100) / 100;
    }

    protected formatCurrency(currency: string, demo: boolean): string {
        return demo ? "FUN" : currency.toUpperCase();
    }

    protected generateTransactionId(prefix: string = "trx"): string {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    protected generateBetId(roundId: string): string {
        return `bet_${roundId}_${Date.now()}`;
    }

    protected extractCustomerFromToken(gameTokenData: any): string {
        return gameTokenData.customer || gameTokenData.playerCode;
    }

    protected extractTokenFromGameData(gameTokenData: any): string {
        return gameTokenData.token || gameTokenData.privateToken;
    }

    protected extractGameId(gameTokenData: any): string {
        return gameTokenData.gameId || gameTokenData.gameCode;
    }

    protected extractCurrency(gameTokenData: any): string {
        return this.formatCurrency(gameTokenData.currency, gameTokenData.demo || false);
    }

    protected buildFreespinInfo(freeSpinData?: any): any {
        if (!freeSpinData) {
            return undefined;
        }

        return {
            freespinRef: freeSpinData.freespinRef,
            requested: freeSpinData.requested || false,
            remainingRounds: freeSpinData.remainingRounds,
            totalWinnings: freeSpinData.totalWinnings
        };
    }

    protected buildPromoInfo(promoType?: string, promoRef?: string, freeSpinData?: any): any {
        if (!promoType || !promoRef) {
            return undefined;
        }

        return {
            promoType,
            promoRef,
            freeSpinData: this.buildFreespinInfo(freeSpinData)
        };
    }
}

export const successResponses = [200, 201, 202];
