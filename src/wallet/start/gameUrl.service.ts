import { Injectable } from "@nestjs/common";
import { CreateGameUrlSupport, CreateGameUrlRequest } from "@skywind-group/sw-integration-core";
import { IntegrationInitRequest } from "@entities/pronet.entities";
import { MerchantGameURLInfo } from "@skywind-group/sw-wallet-adapter-core";
import { logging, measures } from "@skywind-group/sw-utils";
import { buildGameUrl, createDemoGameParams, createRealGameParams } from "@utils/pronet.utils";
import config from "@config";

const log = logging.logger("GameUrlService");
const { measure } = measures;

@Injectable()
export class GameUrlService implements CreateGameUrlSupport<IntegrationInitRequest> {
    
    @measure({ name: "GameUrlService.createGameUrl", isAsync: true })
    public async createGameUrl(
        req: CreateGameUrlRequest<IntegrationInitRequest>
    ): Promise<MerchantGameURLInfo> {
        const baseUrl = config.http.operatorUrl;
        const request = req.initRequest;

        let gameParams: any;

        if (request.demo) {
            gameParams = createDemoGameParams(
                request.gameId,
                request.lang,
                request.platform,
                request.trader,
                request.lobby,
                request.tableId
            );
        } else {
            gameParams = createRealGameParams(
                request.currency,
                request.customer,
                request.gameId,
                request.lang,
                request.platform,
                request.token,
                request.trader,
                request.country,
                request.lobby,
                request.tableId
            );
        }

        const gameUrl = buildGameUrl(`${baseUrl}/casino-engine/game`, gameParams);

        log.info("Created game URL for PCES", {
            customer: request.customer,
            gameId: request.gameId,
            demo: request.demo,
            url: gameUrl
        });

        return {
            urlParams: gameParams,
            tokenData: {
                // Create a minimal token data object
                playerCode: request.customer || "",
                currency: request.currency || "",
                gameCode: req.gameCode,
                providerCode: req.providerCode,
                providerGameCode: req.providerGameCode,

                language: request.lang,
                country: request.country,
                brandId: 1,
                merchantType: "pces",
                merchantCode: "pces"
            }
        };
    }
}
