import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { Test } from "@nestjs/testing";
import { PaymentService } from "./payment.service";
import { HttpGateway } from "@skywind-group/sw-integration-core";
import { BetHttpHandler } from "./bet.http.handler";
import { WinHttpHandler } from "./win.http.handler";
import { BalanceHttpHandler } from "./balance.http.handler";
import { DebitCreditHttpHandler } from "./debitCredit.http.handler";
import { RollbackHttpHandler } from "./rollback.http.handler";
import { PromoHttpHandler } from "./promo.http.handler";
import { IntegrationPaymentRequest } from "@entities/pronet.entities";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";

@suite
class PaymentServiceSpec {
    private paymentService: PaymentService;
    private httpGateway: HttpGateway;

    public async before() {
        const mockHttpGateway = {
            request: async (req: any, handler: any) => {
                return createMockBalance();
            }
        };

        const module = await Test.createTestingModule({
            providers: [
                PaymentService,
                { provide: HttpGateway, useValue: mockHttpGateway },
                { provide: BetHttpHandler, useValue: {} },
                { provide: WinHttpHandler, useValue: {} },
                { provide: BalanceHttpHandler, useValue: {} },
                { provide: DebitCreditHttpHandler, useValue: {} },
                { provide: RollbackHttpHandler, useValue: {} },
                { provide: PromoHttpHandler, useValue: {} }
            ]
        }).compile();

        this.paymentService = module.get<PaymentService>(PaymentService);
        this.httpGateway = module.get<HttpGateway>(HttpGateway);
    }

    @test
    public async "should get balance successfully"() {
        // Arrange
        const request = createMockPaymentRequest();
        const expectedBalance = createMockBalance();

        // Act
        const result = await this.paymentService.getBalance(request);

        // Assert
        expect(result).to.deep.equal(expectedBalance);
    }

    @test
    public async "should process bet payment successfully"() {
        // Arrange
        const request = createMockPaymentRequest();
        request.request.bet = 10.00;
        const expectedBalance = createMockBalance();

        // Act
        const result = await this.paymentService.commitBetPayment(request);

        // Assert
        expect(result).to.deep.equal(expectedBalance);
    }

    @test
    public async "should process win payment successfully"() {
        // Arrange
        const request = createMockPaymentRequest();
        request.request.totalWin = 25.00;
        const expectedBalance = createMockBalance();

        // Act
        const result = await this.paymentService.commitWinPayment(request);

        // Assert
        expect(result).to.deep.equal(expectedBalance);
    }

    @test
    public async "should process jackpot win payment successfully"() {
        // Arrange
        const request = createMockPaymentRequest();
        request.request.totalWin = 1000.00;
        const expectedBalance = createMockBalance();

        // Act
        const result = await this.paymentService.commitJackpotWinPayment(request);

        // Assert
        expect(result).to.deep.equal(expectedBalance);
    }

    @test
    public async "should process bonus payment successfully"() {
        // Arrange
        const request = createMockPaymentRequest();
        request.request.totalWin = 50.00;
        request.request.promoType = "freespin";
        const expectedBalance = createMockBalance();

        // Act
        const result = await this.paymentService.commitBonusPayment(request);

        // Assert
        expect(result).to.deep.equal(expectedBalance);
    }

    @test
    public async "should return balance for zero bet"() {
        // Arrange
        const request = createMockPaymentRequest();
        request.request.bet = 0;
        const expectedBalance = createMockBalance();

        // Act
        const result = await this.paymentService.commitBetPayment(request);

        // Assert
        expect(result).to.deep.equal(expectedBalance);
    }

    @test
    public async "should return balance for zero win"() {
        // Arrange
        const request = createMockPaymentRequest();
        request.request.totalWin = 0;
        const expectedBalance = createMockBalance();

        // Act
        const result = await this.paymentService.commitWinPayment(request);

        // Assert
        expect(result).to.deep.equal(expectedBalance);
    }
}

// Test helper functions
function createMockPaymentRequest(): IntegrationPaymentRequest {
    return {
        gameTokenData: {
            customer: "test_customer",
            token: "test_token",
            gameId: "test_game",
            currency: "USD",
            demo: false,
            platform: "d" as any,
            language: "en",
            country: "US",
            trader: "test_trader",
            playerCode: "test_customer",
            gameCode: "test_game",
            isPromoInternal: false,
            brandId: 1,
            merchantType: "pces",
            merchantCode: "test_merchant"
        },
        request: {
            bet: 10.00,
            totalWin: 0,
            transactionId: {
                publicId: "test_txn_123",
                serialId: 123,
                timestamp: Date.now()
            },
            roundPID: "test_round_123",
            gameToken: "test_token",
            ts: new Date().toISOString(),
            roundId: "test_round_123"
        },
        merchantInfo: {
            code: "test_merchant",
            type: "pces",
            params: {},
            brandId: 1
        }
    };
}

function createMockBalance(): Balance {
    return {
        main: 100.50
    };
}
