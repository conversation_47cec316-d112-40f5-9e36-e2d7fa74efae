import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";
import { Base<PERSON>ttp<PERSON><PERSON><PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import {
    IntegrationGetBalanceRequest,
    IntegrationPaymentRequest
} from "@entities/pronet.entities";
import {
    PronetAuthRequest,
    PronetAuthResponse
} from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";

@Injectable()
export class BalanceHttpHandler extends BaseHttpHandler 
    implements HttpHandler<IntegrationGetBalanceRequest | IntegrationPaymentRequest, Balance> {
    
    public async build(req: IntegrationGetBalanceRequest | IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);
        
        this.validateRequiredFields(gameTokenData, ["customer", "token"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);

        const authRequest: PronetAuthRequest = this.buildPronetRequest(customer, token);

        return this.buildHttpRequest({
            endpoint: "auth",
            method: "post",
            payload: authRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const pronetResponse = this.parseHttpResponse<PronetAuthResponse>(response);

        return {
            main: this.sanitizeAmount(pronetResponse.balance + (pronetResponse.bonusBalance || 0))
        };
    }
}
