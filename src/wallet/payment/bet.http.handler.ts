import { CommitPaymentRequest, <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/pronet.entities";
import {
    PronetDebitRequest,
    PronetDebitResponse
} from "@entities/operator.entities";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { sumMajorUnits } from "@utils/pronet.utils";
import { Injectable } from "@nestjs/common";

@Injectable()
export class BetHttpHandler extends Base<PERSON>ttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {
    
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);
        
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId", "currency"]);
        this.validateRequiredFields(req.request, ["bet", "transactionId", "roundPID"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const currency = this.extractCurrency(gameTokenData);
        
        const betAmount = this.sanitizeAmount(sumMajorUnits(req.request.bet));
        const betId = this.generateBetId(req.request.roundPID);
        const trxId = this.generateTransactionId("bet");

        const debitRequest: PCESDebitRequest = this.buildPCESRequest(customer, token, {
            gameId,
            amount: betAmount,
            currency,
            betId,
            trxId,
            tip: false // PCES supports tip parameter for some games
        });

        return this.buildHttpRequest({
            endpoint: "debit",
            method: "post",
            payload: debitRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const pcesResponse = this.parseHttpResponse<PCESDebitResponse>(response);

        return {
            main: this.sanitizeAmount(pcesResponse.balance + (pcesResponse.bonusBalance || 0))
        };
    }
}
