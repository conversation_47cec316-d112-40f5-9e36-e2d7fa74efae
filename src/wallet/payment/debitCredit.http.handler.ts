import { CommitPaymentRequest, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/pronet.entities";
import { BaseHttp<PERSON><PERSON><PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    PronetDebitCreditRequest,
    PronetDebitCreditResponse
} from "@entities/operator.entities";
import { sumMajorUnits } from "@utils/pronet.utils";
import { Injectable } from "@nestjs/common";

@Injectable()
export class DebitCreditHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {
    
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);
        
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId", "currency"]);
        this.validateRequiredFields(req.request, ["bet", "totalWin", "transactionId", "roundPID"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const currency = this.extractCurrency(gameTokenData);
        
        const betAmount = this.sanitizeAmount(sumMajorUnits(req.request.bet));
        const winAmount = this.sanitizeAmount(sumMajorUnits(req.request.totalWin));
        const betId = this.generateBetId(req.request.roundPID);
        const trxId = this.generateTransactionId("bet");
        const creditTrxId = this.generateTransactionId("win");

        const debitCreditRequest: PCESDebitCreditRequest = this.buildPCESRequest(customer, token, {
            gameId,
            amount: betAmount,
            creditAmount: winAmount,
            currency,
            betId,
            trxId,
            creditTrxId,
            tip: false
        });

        return this.buildHttpRequest({
            endpoint: "debit-credit",
            method: "post",
            payload: debitCreditRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const pcesResponse = this.parseHttpResponse<PCESDebitCreditResponse>(response);
        
        return {
            main: this.sanitizeAmount(pcesResponse.balance + (pcesResponse.bonusBalance || 0))
        };
    }
}
