import { CommitPaymentRequest, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/pronet.entities";
import { BaseHttp<PERSON><PERSON><PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    PronetCreditRequest,
    PronetCreditResponse
} from "@entities/operator.entities";
import { sumMajorUnits } from "@utils/pronet.utils";
import { Injectable } from "@nestjs/common";

@Injectable()
export class WinHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {
    
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);
        
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId", "currency"]);
        this.validateRequiredFields(req.request, ["totalWin", "transactionId", "roundPID"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const currency = this.extractCurrency(gameTokenData);
        
        const winAmount = this.sanitizeAmount(sumMajorUnits(req.request.totalWin));
        const betId = this.generateBetId(req.request.roundPID);
        const trxId = this.generateTransactionId("win");

        const creditRequest: PCESCreditRequest = this.buildPCESRequest(customer, token, {
            gameId,
            amount: winAmount,
            currency,
            betId,
            trxId,
            freespin: this.buildFreespinInfo(req.freeSpinData)
        });

        return this.buildHttpRequest({
            endpoint: "credit",
            method: "post",
            payload: creditRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const pcesResponse = this.parseHttpResponse<PCESCreditResponse>(response);
        
        return {
            main: this.sanitizeAmount(pcesResponse.balance + (pcesResponse.bonusBalance || 0))
        };
    }
}
