"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameTokenService = void 0;
const common_1 = require("@nestjs/common");
const sw_utils_1 = require("@skywind-group/sw-utils");
const pronet_errors_1 = require("@errors/pronet.errors");
const log = sw_utils_1.logging.logger("GameTokenService");
const { measure } = sw_utils_1.measures;
let GameTokenService = class GameTokenService {
    async createGameTokenData(req) {
        this.validateRequest(req);
        const gameTokenData = {
            playerCode: req.startGameToken.playerCode,
            currency: req.currency,
            gameCode: req.gameCode,
            customer: req.startGameToken.customer,
            token: req.startGameToken.token,
            gameId: req.startGameToken.gameId,
            demo: req.startGameToken.demo,
            platform: req.startGameToken.platform,
            language: req.startGameToken.language,
            country: req.startGameToken.country,
            trader: req.startGameToken.trader,
            tableId: req.startGameToken.tableId,
            lobby: req.startGameToken.lobby,
            isPromoInternal: false,
            brandId: req.startGameToken.brandId,
            merchantType: req.startGameToken.merchantType,
            merchantCode: req.startGameToken.merchantCode
        };
        log.info("Created game token for PCES", {
            customer: gameTokenData.customer,
            gameId: gameTokenData.gameId,
            demo: gameTokenData.demo
        });
        return {
            gameTokenData
        };
    }
    validateRequest(req) {
        if (!req.gameCode || req.gameCode.trim() === "") {
            throw new pronet_errors_1.ValidationError("Game code is required");
        }
        if (!req.startGameToken.gameId || req.startGameToken.gameId.trim() === "") {
            throw new pronet_errors_1.ValidationError("Game ID is required");
        }
        if (!req.startGameToken.trader || req.startGameToken.trader.trim() === "") {
            throw new pronet_errors_1.ValidationError("Trader is required");
        }
        if (!req.startGameToken.demo && (!req.startGameToken.customer || !req.startGameToken.token)) {
            throw new pronet_errors_1.ValidationError("Customer and token are required for real money games");
        }
        if (!req.startGameToken.demo && (!req.currency || req.currency.trim() === "")) {
            throw new pronet_errors_1.ValidationError("Currency is required for real money games");
        }
    }
};
exports.GameTokenService = GameTokenService;
__decorate([
    measure({ name: "GameTokenService.createGameTokenData", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GameTokenService.prototype, "createGameTokenData", null);
exports.GameTokenService = GameTokenService = __decorate([
    (0, common_1.Injectable)()
], GameTokenService);
//# sourceMappingURL=gameToken.service.js.map