"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryController = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("@nestjs/common");
const history_service_1 = require("../services/history.service");
const operator_entities_1 = require("../../entities/operator.entities");
const sw_integration_core_1 = require("@skywind-group/sw-integration-core");
let HistoryController = class HistoryController {
    constructor(historyService) {
        this.historyService = historyService;
    }
    async getGameHistoryImageUrl(request) {
        const imageUrl = await this.historyService.getGameHistoryImageUrl(request);
        return { imageUrl };
    }
    async getGameHistoryDetails(request) {
        const details = await this.historyService.getGameHistoryDetails(request);
        return details;
    }
};
exports.HistoryController = HistoryController;
__decorate([
    (0, common_1.Get)("/image"),
    (0, common_1.UseFilters)(sw_integration_core_1.ErrorFilter),
    __param(0, (0, common_1.Query)(new common_2.ValidationPipe())),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_a = typeof operator_entities_1.PCESHistoryRequest !== "undefined" && operator_entities_1.PCESHistoryRequest) === "function" ? _a : Object]),
    __metadata("design:returntype", Promise)
], HistoryController.prototype, "getGameHistoryImageUrl", null);
__decorate([
    (0, common_1.Get)("/details"),
    (0, common_1.UseFilters)(sw_integration_core_1.ErrorFilter),
    __param(0, (0, common_1.Query)(new common_2.ValidationPipe())),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof operator_entities_1.PCESHistoryRequest !== "undefined" && operator_entities_1.PCESHistoryRequest) === "function" ? _b : Object]),
    __metadata("design:returntype", Promise)
], HistoryController.prototype, "getGameHistoryDetails", null);
exports.HistoryController = HistoryController = __decorate([
    (0, common_1.Controller)("history"),
    __metadata("design:paramtypes", [history_service_1.HistoryService])
], HistoryController);
//# sourceMappingURL=history.controller.js.map