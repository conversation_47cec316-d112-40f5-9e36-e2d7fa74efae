"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PCESHistoryRequest = exports.pcesStatusMessages = exports.pcesErrorCodes = exports.PronetHistoryRequest = exports.pronetStatusMessages = exports.pronetErrorCodes = exports.operatorStatusMessages = exports.operatorErrorCodes = exports.OperatorHistoryRequest = exports.IntegrationGameLaunchRequest = exports.OperatorPromoType = exports.OperatorPlatformType = void 0;
const class_validator_1 = require("class-validator");
var OperatorPlatformType;
(function (OperatorPlatformType) {
    OperatorPlatformType["desktop"] = "d";
    OperatorPlatformType["mobile"] = "m";
})(OperatorPlatformType || (exports.OperatorPlatformType = OperatorPlatformType = {}));
var OperatorPromoType;
(function (OperatorPromoType) {
    OperatorPromoType["FSW"] = "FSW";
    OperatorPromoType["JPW"] = "JPW";
    OperatorPromoType["CB"] = "CB";
    OperatorPromoType["TW"] = "TW";
    OperatorPromoType["RW"] = "RW";
    OperatorPromoType["REW"] = "REW";
    OperatorPromoType["CDW"] = "CDW";
    OperatorPromoType["RB"] = "RB";
})(OperatorPromoType || (exports.OperatorPromoType = OperatorPromoType = {}));
class IntegrationGameLaunchRequest {
}
exports.IntegrationGameLaunchRequest = IntegrationGameLaunchRequest;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "gameId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "lang", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "trader", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "platform", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "currency", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "customer", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "token", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "tableId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "lobby", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "country", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], IntegrationGameLaunchRequest.prototype, "demo", void 0);
class OperatorHistoryRequest {
}
exports.OperatorHistoryRequest = OperatorHistoryRequest;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], OperatorHistoryRequest.prototype, "customer", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], OperatorHistoryRequest.prototype, "roundId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], OperatorHistoryRequest.prototype, "gameId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], OperatorHistoryRequest.prototype, "lang", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], OperatorHistoryRequest.prototype, "trader", void 0);
exports.operatorErrorCodes = {
    SUCCESS: 0,
    UNKNOWN_ERROR: -1,
    UNAUTHORIZED_REQUEST: -2,
    NOT_INTEGRATED: -3,
    TOKEN_CUSTOMER_MISMATCH: -4,
    UNSUPPORTED_API_VERSION: -5,
    INTERNAL_CACHE_ERROR: -6,
    PROMOTION_TYPE_NOT_SUPPORTED: -7,
    BET_RECORD_NOT_FOUND: -20120,
    BET_ALREADY_WON: -20112,
    AUTHENTICATION_FAILED: -20101,
    GAME_NOT_FOUND: -20130,
    BET_LIMIT_REACHED: -20201,
    LOSS_LIMIT_REACHED: -20202,
    SESSION_LIMIT_REACHED: -20203,
    PROFIT_LIMIT_REACHED: -20204,
    INVALID_CASINO_VENDOR: -20301,
    ALL_BET_ARE_OFF: -20302,
    INVALID_GAME: -20303,
    CUSTOMER_NOT_FOUND: -20304,
    INVALID_CURRENCY: -20305,
    INSUFFICIENT_FUNDS: -20306,
    PLAYER_SUSPENDED: -20307,
    REQUIRED_FIELD_MISSING: -20308,
    DATA_OUT_OF_RANGE: -20309,
    BET_ALREADY_SETTLED: -20310,
    TOKEN_NOT_FOUND: -20316,
    TOKEN_TIMEOUT: -20311,
    TOKEN_INVALID: -20312,
    TRANSACTION_NOT_FOUND: -20313,
    NEGATIVE_DEPOSIT: -20314,
    NEGATIVE_WITHDRAWAL: -20315
};
exports.operatorStatusMessages = {
    [exports.operatorErrorCodes.SUCCESS]: "SUCCESS",
    [exports.operatorErrorCodes.UNKNOWN_ERROR]: "UNKNOWN_ERROR",
    [exports.operatorErrorCodes.UNAUTHORIZED_REQUEST]: "UNAUTHORIZED_REQUEST",
    [exports.operatorErrorCodes.NOT_INTEGRATED]: "NOT_INTEGRATED",
    [exports.operatorErrorCodes.TOKEN_CUSTOMER_MISMATCH]: "TOKEN_CUSTOMER_MISMATCH",
    [exports.operatorErrorCodes.UNSUPPORTED_API_VERSION]: "UNSUPPORTED_API_VERSION",
    [exports.operatorErrorCodes.INTERNAL_CACHE_ERROR]: "INTERNAL_CACHE_ERROR",
    [exports.operatorErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED]: "PROMOTION_TYPE_NOT_SUPPORTED",
    [exports.operatorErrorCodes.BET_RECORD_NOT_FOUND]: "BET_RECORD_NOT_FOUND",
    [exports.operatorErrorCodes.BET_ALREADY_WON]: "BET_ALREADY_WON",
    [exports.operatorErrorCodes.AUTHENTICATION_FAILED]: "AUTHENTICATION_FAILED",
    [exports.operatorErrorCodes.GAME_NOT_FOUND]: "GAME_NOT_FOUND",
    [exports.operatorErrorCodes.BET_LIMIT_REACHED]: "BET_LIMIT_REACHED",
    [exports.operatorErrorCodes.LOSS_LIMIT_REACHED]: "LOSS_LIMIT_REACHED",
    [exports.operatorErrorCodes.SESSION_LIMIT_REACHED]: "SESSION_LIMIT_REACHED",
    [exports.operatorErrorCodes.PROFIT_LIMIT_REACHED]: "PROFIT_LIMIT_REACHED",
    [exports.operatorErrorCodes.INVALID_CASINO_VENDOR]: "INVALID_CASINO_VENDOR",
    [exports.operatorErrorCodes.ALL_BET_ARE_OFF]: "ALL_BET_ARE_OFF",
    [exports.operatorErrorCodes.INVALID_GAME]: "INVALID_GAME",
    [exports.operatorErrorCodes.CUSTOMER_NOT_FOUND]: "CUSTOMER_NOT_FOUND",
    [exports.operatorErrorCodes.INVALID_CURRENCY]: "INVALID_CURRENCY",
    [exports.operatorErrorCodes.INSUFFICIENT_FUNDS]: "INSUFFICIENT_FUNDS",
    [exports.operatorErrorCodes.PLAYER_SUSPENDED]: "PLAYER_SUSPENDED",
    [exports.operatorErrorCodes.REQUIRED_FIELD_MISSING]: "REQUIRED_FIELD_MISSING",
    [exports.operatorErrorCodes.DATA_OUT_OF_RANGE]: "DATA_OUT_OF_RANGE",
    [exports.operatorErrorCodes.BET_ALREADY_SETTLED]: "BET_ALREADY_SETTLED",
    [exports.operatorErrorCodes.TOKEN_NOT_FOUND]: "TOKEN_NOT_FOUND",
    [exports.operatorErrorCodes.TOKEN_TIMEOUT]: "TOKEN_TIMEOUT",
    [exports.operatorErrorCodes.TOKEN_INVALID]: "TOKEN_INVALID",
    [exports.operatorErrorCodes.TRANSACTION_NOT_FOUND]: "TRANSACTION_NOT_FOUND",
    [exports.operatorErrorCodes.NEGATIVE_DEPOSIT]: "NEGATIVE_DEPOSIT",
    [exports.operatorErrorCodes.NEGATIVE_WITHDRAWAL]: "NEGATIVE_WITHDRAWAL"
};
exports.pronetErrorCodes = exports.operatorErrorCodes;
exports.pronetStatusMessages = exports.operatorStatusMessages;
exports.PronetHistoryRequest = OperatorHistoryRequest;
exports.pcesErrorCodes = exports.operatorErrorCodes;
exports.pcesStatusMessages = exports.operatorStatusMessages;
exports.PCESHistoryRequest = OperatorHistoryRequest;
//# sourceMappingURL=operator.entities.js.map