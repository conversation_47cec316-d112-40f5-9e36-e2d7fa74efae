"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateNumber = generateNumber;
exports.generateUniqueId = generateUniqueId;
exports.sumMajorUnits = sumMajorUnits;
exports.convertToMinorUnits = convertToMinorUnits;
exports.convertFromMinorUnits = convertFromMinorUnits;
exports.calculateOperatorHash = calculateOperatorHash;
exports.buildOperatorApiUrl = buildOperatorApiUrl;
exports.buildOperatorHeaders = buildOperatorHeaders;
exports.buildGameUrl = buildGameUrl;
exports.isTokenExpired = isTokenExpired;
exports.formatCurrency = formatCurrency;
exports.parsePlatformType = parsePlatformType;
exports.generateBetId = generateBetId;
exports.generateTransactionId = generateTransactionId;
exports.isOperatorSuccess = isOperatorSuccess;
exports.extractOperatorError = extractOperatorError;
exports.buildRoundDetailsUrl = buildRoundDetailsUrl;
exports.sanitizeGameId = sanitizeGameId;
exports.sanitizeTableId = sanitizeTableId;
exports.formatDateForOperator = formatDateForOperator;
exports.createDemoGameParams = createDemoGameParams;
exports.createRealGameParams = createRealGameParams;
const CryptoJS = require("crypto-js");
const _config_1 = require("../config");
function generateNumber(length) {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
function generateUniqueId() {
    return `${Date.now()}_${generateNumber(6)}`;
}
function sumMajorUnits(...amounts) {
    const total = amounts.reduce((sum, amount) => sum + (amount || 0), 0);
    return Math.round(total * _config_1.default.currencyUnitMultiplier) / _config_1.default.currencyUnitMultiplier;
}
function convertToMinorUnits(amount) {
    return Math.round(amount * _config_1.default.currencyUnitMultiplier);
}
function convertFromMinorUnits(amount) {
    return amount / _config_1.default.currencyUnitMultiplier;
}
function calculateOperatorHash(requestBody) {
    const jsonString = JSON.stringify(requestBody);
    const toHash = jsonString + _config_1.default.operator.genericSecretKey;
    return CryptoJS.SHA256(toHash).toString();
}
function buildOperatorApiUrl(endpoint) {
    const baseUrl = _config_1.default.http.operatorUrl;
    const vendorCode = _config_1.default.operator.vendorCode;
    const apiVersion = _config_1.default.operator.apiVersion;
    return `${baseUrl}/casino-engine/generic/${vendorCode}/${apiVersion}/${endpoint}`;
}
function buildOperatorHeaders(hash) {
    return {
        "Content-Type": "application/json",
        "Generic-Id": _config_1.default.operator.genericId,
        "Hash": hash
    };
}
function buildGameUrl(baseUrl, params) {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
            queryParams.append(key, params[key].toString());
        }
    });
    return `${baseUrl}?${queryParams.toString()}`;
}
function isTokenExpired(tokenCreatedAt) {
    const now = new Date();
    const expirationTime = new Date(tokenCreatedAt.getTime() + (_config_1.default.operator.tokenExpirationMinutes * 60 * 1000));
    return now > expirationTime;
}
function formatCurrency(currency, demo) {
    return demo ? "FUN" : currency.toUpperCase();
}
function parsePlatformType(platform) {
    switch (platform.toLowerCase()) {
        case "desktop":
        case "d":
            return "d";
        case "mobile":
        case "m":
            return "m";
        default:
            return "d";
    }
}
function generateBetId(roundId) {
    return `bet_${roundId}_${Date.now()}`;
}
function generateTransactionId(type = "bet") {
    return `${type}_${Date.now()}_${generateNumber(6)}`;
}
function isOperatorSuccess(response) {
    return response && response.code === 0 && response.status === "SUCCESS";
}
function extractOperatorError(response) {
    if (response && response.code !== 0) {
        return {
            code: response.code,
            message: response.status || "Unknown error"
        };
    }
    return {
        code: -1,
        message: "Unknown error"
    };
}
function buildRoundDetailsUrl(baseUrl, params) {
    return buildGameUrl(baseUrl, params);
}
function sanitizeGameId(gameId) {
    return gameId.trim().substring(0, 50);
}
function sanitizeTableId(tableId) {
    if (!tableId || tableId.trim() === "") {
        return null;
    }
    return tableId.trim().substring(0, 50);
}
function formatDateForOperator(date) {
    return date.toISOString();
}
function createDemoGameParams(gameId, lang, platform, trader, lobby, tableId) {
    return {
        currency: "FUN",
        demo: true,
        gameId: sanitizeGameId(gameId),
        lang: lang.toLowerCase(),
        lobby: lobby,
        platform: parsePlatformType(platform),
        tableId: sanitizeTableId(tableId),
        trader: trader
    };
}
function createRealGameParams(currency, customer, gameId, lang, platform, token, trader, country, lobby, tableId) {
    return {
        currency: currency.toUpperCase(),
        customer: customer,
        demo: false,
        gameId: sanitizeGameId(gameId),
        lang: lang.toLowerCase(),
        lobby: lobby,
        platform: parsePlatformType(platform),
        tableId: sanitizeTableId(tableId),
        token: token,
        trader: trader,
        country: country
    };
}
//# sourceMappingURL=operator.utils.js.map